{"data_mtime": 1754418752, "dep_lines": [7, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 38, 148, 596, 783, 784, 864, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 20, 20, 20, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["logging", "re", "flask", "markupsafe", "config_manager", "monitor_service", "models", "user_management_service", "repository_backends", "document_service", "historical_scanner", "document_processor", "time", "diff_service", "shutil", "os", "datetime", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "file_manager", "flask.app", "flask.config", "flask.globals", "flask.helpers", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "flask.templating", "flask.wrappers", "jinja2", "jinja2.environment", "ollama_client", "typing", "typing_extensions", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "0319889a5e3eb43d00150fe180489fa90402e8c0", "id": "web_interface", "ignore_all": false, "interface_hash": "a9223b3a424cef7a8ac8ad059790baa09f2dcf67", "mtime": 1754418896, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\web_interface.py", "plugin_data": null, "size": 55341, "suppressed": [], "version_id": "1.15.0"}