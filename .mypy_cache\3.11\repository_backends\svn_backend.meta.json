{"data_mtime": 1754415321, "dep_lines": [9, 9, 11, 16, 7, 8, 9, 10, 12, 13, 14, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 10, 10, 20, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["xml.etree.ElementTree", "xml.etree", "urllib.parse", "repository_backends.base", "logging", "subprocess", "xml", "typing", "datetime", "requests", "re", "models", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "http", "http.cookiejar", "os", "requests.auth", "requests.exceptions", "requests.models", "requests.structures", "typing_extensions"], "hash": "0ed9de13f4b872b7c910e54575375b85b45cddfd", "id": "repository_backends.svn_backend", "ignore_all": false, "interface_hash": "1312c6a8d73adfc32bda96623f9a60e2bdf32f31", "mtime": 1754415351, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\repository_backends\\svn_backend.py", "plugin_data": null, "size": 59652, "suppressed": [], "version_id": "1.15.0"}