{".class": "MypyFile", "_fullname": "web_interface", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "cross_ref": "models.Config", "kind": "Gdef"}, "ConfigManager": {".class": "SymbolTableNode", "cross_ref": "config_manager.ConfigManager", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "document_service.Document", "kind": "Gdef"}, "DocumentService": {".class": "SymbolTableNode", "cross_ref": "document_service.DocumentService", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "HistoricalScanConfig": {".class": "SymbolTableNode", "cross_ref": "models.HistoricalScanConfig", "kind": "Gdef"}, "HistoricalScanStatus": {".class": "SymbolTableNode", "cross_ref": "models.HistoricalScanStatus", "kind": "Gdef"}, "HistoricalScanner": {".class": "SymbolTableNode", "cross_ref": "historical_scanner.HistoricalScanner", "kind": "Gdef"}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef"}, "MonitorService": {".class": "SymbolTableNode", "cross_ref": "monitor_service.MonitorService", "kind": "Gdef"}, "RepositoryConfig": {".class": "SymbolTableNode", "cross_ref": "models.RepositoryConfig", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "models.User", "kind": "Gdef"}, "UserManagementService": {".class": "SymbolTableNode", "cross_ref": "user_management_service.UserManagementService", "kind": "Gdef"}, "UserRole": {".class": "SymbolTableNode", "cross_ref": "models.UserRole", "kind": "Gdef"}, "WebInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "web_interface.WebInterface", "name": "WebInterface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "web_interface.WebInterface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "web_interface", "mro": ["web_interface.WebInterface", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "monitor_service"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_interface.WebInterface.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "monitor_service"], "arg_types": ["web_interface.WebInterface", "monitor_service.MonitorService"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of WebInterface", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.app", "name": "app", "type": "flask.app.Flask"}}, "backend_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.backend_manager", "name": "backend_manager", "type": "repository_backends.RepositoryBackendManager"}}, "config_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.config_manager", "name": "config_manager", "type": "config_manager.ConfigManager"}}, "document_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.document_service", "name": "document_service", "type": "document_service.DocumentService"}}, "file_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.file_manager", "name": "file_manager", "type": "file_manager.FileManager"}}, "historical_scanner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.historical_scanner", "name": "historical_scanner", "type": "historical_scanner.HistoricalScanner"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.logger", "name": "logger", "type": "logging.Logger"}}, "markdown_to_html": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_interface.WebInterface.markdown_to_html", "name": "markdown_to_html", "type": null}}, "monitor_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.monitor_service", "name": "monitor_service", "type": "monitor_service.MonitorService"}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_interface.WebInterface.run", "name": "run", "type": null}}, "setup_routes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "web_interface.WebInterface.setup_routes", "name": "setup_routes", "type": null}}, "user_service": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "web_interface.WebInterface.user_service", "name": "user_service", "type": "user_management_service.UserManagementService"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "web_interface.WebInterface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "web_interface.WebInterface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_interface.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_interface.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_interface.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_interface.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_interface.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "web_interface.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "flash": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.flash", "kind": "Gdef"}, "get_backend_manager": {".class": "SymbolTableNode", "cross_ref": "repository_backends.get_backend_manager", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "redirect": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.redirect", "kind": "Gdef"}, "render_template": {".class": "SymbolTableNode", "cross_ref": "flask.templating.render_template", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "url_for": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.url_for", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\web_interface.py"}