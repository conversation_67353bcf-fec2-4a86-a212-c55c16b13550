{"data_mtime": 1754168700, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "json", "time", "pickle", "<PERSON><PERSON><PERSON>", "logging", "threading", "pathlib", "typing", "datetime", "builtins", "_collections_abc", "_frozen_importlib", "_hashlib", "_io", "_pickle", "_thread", "_typeshed", "abc", "io", "types", "typing_extensions"], "hash": "14fe25e385168420c9ae723e863e732d6879d617", "id": "cache_manager", "ignore_all": false, "interface_hash": "4a9fc977796f59ce9f95031bd4c4e9987a2af69b", "mtime": 1754349386, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\cache_manager.py", "plugin_data": null, "size": 10615, "suppressed": [], "version_id": "1.15.0"}