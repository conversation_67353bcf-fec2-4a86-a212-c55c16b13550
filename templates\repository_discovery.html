{% extends "base.html" %}

{% block title %}Repository Discovery - RepoSense AI{% endblock %}

{% block extra_css %}
<style>
.depth-selector {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    background: #f8f9fa;
    margin-bottom: 1rem;
}

.depth-selector .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

.depth-selector .form-select {
    font-size: 0.95rem;
    padding: 0.75rem;
    border: 2px solid #dee2e6;
}

.depth-selector .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.depth-help {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 0.75rem;
    min-height: 120px;
}

.depth-help strong {
    color: #0d6efd;
}

.depth-help .text-muted {
    line-height: 1.4;
}

/* Responsive repository display */
.repo-card {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 1rem;
    background: white;
}

.repo-header {
    background: #f8f9fa;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.repo-header:hover {
    background: #e9ecef;
}

.repo-branches {
    padding: 0;
}

.branch-item {
    padding: 0.5rem 1rem;
    border-bottom: 1px solid #f1f3f4;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.branch-item:last-child {
    border-bottom: none;
}

.branch-info {
    flex: 1;
    min-width: 200px;
}

.branch-meta {
    display: flex;
    gap: 1rem;
    font-size: 0.875rem;
    color: #6c757d;
    flex-wrap: wrap;
}

.branch-actions {
    flex-shrink: 0;
}

/* Branch type styling */
.branch-trunk { background-color: rgba(25, 135, 84, 0.05); }
.branch-branches { background-color: rgba(13, 110, 253, 0.05); }
.branch-tags { background-color: rgba(255, 193, 7, 0.05); }

.branch-type-legend {
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.branch-type-legend .badge {
    margin-right: 0.5rem;
}

/* Compact desktop table */
.compact-table {
    font-size: 0.9rem;
}

.compact-table th {
    padding: 0.5rem 0.75rem;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
}

.compact-table td {
    padding: 0.5rem 0.75rem;
    vertical-align: middle;
}

.repo-name {
    font-weight: 600;
    color: #495057;
    max-width: 120px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.branch-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.branch-badge.trunk { background: rgba(25, 135, 84, 0.1); color: #198754; }
.branch-badge.branches { background: rgba(13, 110, 253, 0.1); color: #0d6efd; }
.branch-badge.tags { background: rgba(255, 193, 7, 0.1); color: #ffc107; }

.revision-info {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #6c757d;
}

.author-info {
    font-size: 0.8rem;
    color: #6c757d;
    max-width: 80px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .branch-meta {
        flex-direction: column;
        gap: 0.25rem;
    }

    .branch-item {
        flex-direction: column;
        align-items: flex-start;
    }

    .branch-actions {
        align-self: flex-end;
        margin-top: 0.5rem;
    }
}

/* Always show cards on mobile, compact table on desktop */
@media (min-width: 768px) {
    .repo-cards {
        display: none;
    }
    .repo-table {
        display: block;
    }
}

@media (max-width: 767px) {
    .repo-cards {
        display: block;
    }
    .repo-table {
        display: none;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Repository Discovery</h1>
            <p class="page-subtitle">Discover and import repositories from SVN servers</p>
        </div>
        <a href="{{ url_for('repositories_page') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Repositories
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-search"></i>Discovery Settings</h5>
            </div>
            <div class="card-body">
                {% if not config.svn_server_url %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tip:</strong> Configure your SVN server settings in
                    <a href="{{ url_for('config_page') }}" class="alert-link">Settings</a>
                    to pre-populate these fields.
                </div>
                {% endif %}
                <form id="discoveryForm">
                    <div class="mb-3">
                        <label for="base_url" class="form-label">SVN Server Base URL *</label>
                        <input type="text" class="form-control" id="base_url" name="base_url"
                               value="{{ config.svn_server_url or '' }}"
                               placeholder="http://your-server:port/svn">
                        <div class="form-text">Base URL of your SVN server (configured in <a href="{{ url_for('config_page') }}">Settings</a>)</div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="{{ config.svn_server_username or '' }}">
                        <div class="form-text">Optional: Username for authentication</div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password"
                               value="{{ config.svn_server_password or '' }}">
                        <div class="form-text">Optional: Password for authentication</div>
                    </div>
                    
                    <div class="depth-selector">
                        <label for="max_depth" class="form-label">
                            <i class="fas fa-layer-group"></i> Search Depth
                        </label>
                        <select class="form-select" id="max_depth" name="max_depth">
                            <option value="1">1 level - Immediate subdirectories only</option>
                            <option value="2">2 levels - Standard project structure</option>
                            <option value="3" selected>3 levels - Team/department organization [Recommended]</option>
                            <option value="4">4 levels - Deep organizational hierarchy</option>
                            <option value="5">5 levels - Very deep structure (slower)</option>
                        </select>
                        <div class="depth-help form-text">
                            <strong>Team/Department Structure (Recommended)</strong><br>
                            <small class="text-muted">
                                Searches 3 levels deep. Ideal for organizations with team-based or department-based repository organization.<br><br>
                                <strong>Example paths that would be found:</strong><br>
                                &bull; /svn/team1/project1/<strong>trunk</strong><br>
                                &bull; /svn/department/app/<strong>trunk</strong><br>
                                &bull; /svn/group/service/<strong>branches</strong>
                            </small>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="scanBtn">
                        <i class="fas fa-search"></i> Start Discovery
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i>Discovery Tips</h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>Discovery looks for standard SVN layouts (trunk, branches, tags)</li>
                    <li>Repositories are identified by their structure</li>
                    <li>Use authentication if your server requires it</li>
                    <li>Lower search depth for faster results</li>
                    <li>Discovered repositories start disabled for safety</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-code-branch"></i>Discovered Repositories</h5>
                <div id="discoveryStatus" class="text-muted">
                    Ready to scan
                </div>
            </div>
            <div class="card-body">
                <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Scanning...</span>
                    </div>
                    <p class="mt-2">Scanning for repositories...</p>
                </div>
                
                <div id="noResults" class="text-center py-4" style="display: none;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No Repositories Found</h5>
                    <p class="text-muted">Try adjusting your search parameters or check the server URL.</p>
                </div>
                
                <div id="resultsContainer" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span id="resultsCount" class="text-muted"></span>
                        <button class="btn btn-sm btn-success" onclick="importAllRepositories()">
                            <i class="fas fa-download"></i> Import All
                        </button>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="branch-type-legend">
                                <small class="text-muted">
                                    <strong>Branch Types:</strong>
                                    <span class="badge bg-success"><i class="fas fa-code-branch"></i> Trunk</span>
                                    <span class="badge bg-primary"><i class="fas fa-code-branch"></i> Branches</span>
                                    <span class="badge bg-warning"><i class="fas fa-tag"></i> Tags</span>
                                    <span class="badge bg-secondary"><i class="fas fa-folder"></i> Other</span>
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select form-select-sm" id="branchFilter" onchange="filterResults()">
                                    <option value="all">All Types</option>
                                    <option value="trunk">Trunk Only</option>
                                    <option value="branches">Branches Only</option>
                                    <option value="tags">Tags Only</option>
                                </select>
                                <input type="text" class="form-control form-control-sm" id="repoSearch"
                                       placeholder="Search repositories..." onkeyup="filterResults()">
                            </div>
                        </div>
                    </div>

                    <!-- Card layout for mobile/small screens -->
                    <div class="repo-cards" id="repositoriesCards">
                    </div>

                    <!-- Compact table layout for desktop -->
                    <div class="repo-table">
                        <div class="table-responsive">
                            <table class="table table-hover compact-table">
                                <thead>
                                    <tr>
                                        <th style="width: 15%;">Repository</th>
                                        <th style="width: 25%;">Branch</th>
                                        <th style="width: 10%;">Rev</th>
                                        <th style="width: 12%;">Author</th>
                                        <th style="width: 10%;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="repositoriesTable">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div id="errorMessage" class="alert alert-danger" style="display: none;">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Repository Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Repository Name</label>
                    <input type="text" class="form-control" id="import_name" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Repository URL</label>
                    <input type="text" class="form-control" id="import_url" readonly>
                </div>
                <div class="mb-3">
                    <label for="import_username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="import_username">
                    <div class="form-text">Leave empty if no authentication required</div>
                </div>
                <div class="mb-3">
                    <label for="import_password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="import_password">
                    <div class="form-text">Leave empty if no authentication required</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmImport()">Import Repository</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let discoveredRepositories = [];
let currentImportRepo = null;

document.getElementById('discoveryForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Validate base URL
    const baseUrlField = document.getElementById('base_url');
    const baseUrl = baseUrlField.value.trim();

    // Clear previous validation state
    baseUrlField.classList.remove('is-invalid');

    if (!baseUrl) {
        baseUrlField.classList.add('is-invalid');
        showError('Please enter a base URL');
        baseUrlField.focus();
        return;
    }

    // Basic URL validation
    try {
        new URL(baseUrl);
    } catch (e) {
        baseUrlField.classList.add('is-invalid');
        showError('Please enter a valid URL (e.g., http://sundc:81/svn)');
        baseUrlField.focus();
        return;
    }

    startDiscovery();
});

// Clear validation state when user types
document.getElementById('base_url').addEventListener('input', function() {
    this.classList.remove('is-invalid');
    document.getElementById('errorMessage').style.display = 'none';
});

// Clear any initial validation state on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('base_url').classList.remove('is-invalid');
    document.getElementById('errorMessage').style.display = 'none';
});

function startDiscovery() {
    const formData = new FormData(document.getElementById('discoveryForm'));

    // Trim the base URL to remove any whitespace
    const baseUrl = document.getElementById('base_url').value.trim();
    formData.set('base_url', baseUrl);

    const scanBtn = document.getElementById('scanBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const errorMessage = document.getElementById('errorMessage');
    const discoveryStatus = document.getElementById('discoveryStatus');
    
    // Show loading state
    scanBtn.disabled = true;
    scanBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
    loadingIndicator.style.display = 'block';
    resultsContainer.style.display = 'none';
    noResults.style.display = 'none';
    errorMessage.style.display = 'none';
    discoveryStatus.textContent = 'Scanning...';
    
    fetch('/repositories/discover/scan', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            discoveredRepositories = data.repositories;
            displayResults(data.repositories);
            discoveryStatus.textContent = `Found ${data.repositories.length} repositories`;
        } else {
            showError(data.message);
            discoveryStatus.textContent = 'Scan failed';
        }
    })
    .catch(error => {
        showError('Network error: ' + error.message);
        discoveryStatus.textContent = 'Scan failed';
    })
    .finally(() => {
        scanBtn.disabled = false;
        scanBtn.innerHTML = '<i class="fas fa-search"></i> Start Discovery';
        loadingIndicator.style.display = 'none';
    });
}

function displayResults(repositories) {
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const resultsCount = document.getElementById('resultsCount');
    const repositoriesTable = document.getElementById('repositoriesTable');
    const repositoriesCards = document.getElementById('repositoriesCards');

    if (repositories.length === 0) {
        noResults.style.display = 'block';
        resultsContainer.style.display = 'none';
        return;
    }

    resultsCount.textContent = `Found ${repositories.length} repositories`;
    repositoriesTable.innerHTML = '';
    repositoriesCards.innerHTML = '';

    // Group repositories by base name for cards
    const groupedRepos = {};
    repositories.forEach((repo, index) => {
        const parts = repo.name.split('/');
        const baseRepo = parts[0];
        if (!groupedRepos[baseRepo]) {
            groupedRepos[baseRepo] = [];
        }
        groupedRepos[baseRepo].push({...repo, originalIndex: index});
    });

    // Populate compact table (for larger screens)
    repositories.forEach((repo, index) => {
        const row = document.createElement('tr');
        const {branchType, branchIcon, branchClass, branchBadgeClass} = getBranchInfo(repo.name);
        const parts = repo.name.split('/');
        const baseRepo = parts[0];

        row.innerHTML = `
            <td>
                <div class="repo-name" title="${repo.url}">${baseRepo}</div>
            </td>
            <td>
                <span class="branch-badge ${branchBadgeClass}" title="${repo.url}">
                    ${branchIcon} ${branchType}
                </span>
            </td>
            <td class="revision-info">${repo.last_revision || '-'}</td>
            <td>
                <div class="author-info" title="${repo.last_author || 'Unknown'}">${repo.last_author || '-'}</div>
            </td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="showImportModal(${index})" title="Import ${repo.name}&#10;${repo.url}">
                    <i class="fas fa-download"></i>
                </button>
            </td>
        `;
        repositoriesTable.appendChild(row);
    });

    // Populate cards (for smaller screens)
    Object.keys(groupedRepos).forEach(repoName => {
        const repoGroup = groupedRepos[repoName];
        const card = document.createElement('div');
        card.className = 'repo-card';

        const branchesHtml = repoGroup.map(repo => {
            const {branchType, branchIcon, branchClass} = getBranchInfo(repo.name);
            return `
                <div class="branch-item ${branchClass}">
                    <div class="branch-info">
                        <div><strong>${branchIcon} ${branchType}</strong></div>
                        <div class="branch-meta">
                            <span>Rev: ${repo.last_revision || '-'}</span>
                            <span>Author: ${repo.last_author || '-'}</span>
                        </div>
                    </div>
                    <div class="branch-actions">
                        <button class="btn btn-sm btn-primary" onclick="showImportModal(${repo.originalIndex})">
                            <i class="fas fa-download"></i> Import
                        </button>
                    </div>
                </div>
            `;
        }).join('');

        card.innerHTML = `
            <div class="repo-header" onclick="toggleRepoGroup('${repoName}')">
                <div>
                    <strong>${repoName}</strong>
                    <small class="text-muted">(${repoGroup.length} branches)</small>
                </div>
                <i class="fas fa-chevron-down" id="chevron-${repoName}"></i>
            </div>
            <div class="repo-branches" id="branches-${repoName}">
                ${branchesHtml}
            </div>
        `;

        repositoriesCards.appendChild(card);
    });

    resultsContainer.style.display = 'block';
    noResults.style.display = 'none';
}

function getBranchInfo(repoName) {
    const parts = repoName.split('/');
    const branchInfo = parts.slice(1).join('/');

    let branchType = '';
    let branchIcon = '';
    let branchClass = '';
    let branchBadgeClass = '';

    if (branchInfo.startsWith('trunk')) {
        branchType = 'trunk';
        branchIcon = '<i class="fas fa-code-branch"></i>';
        branchClass = 'branch-trunk';
        branchBadgeClass = 'trunk';
    } else if (branchInfo.startsWith('branches/')) {
        branchType = branchInfo.replace('branches/', '');
        // Truncate long branch names
        if (branchType.length > 20) {
            branchType = branchType.substring(0, 17) + '...';
        }
        branchIcon = '<i class="fas fa-code-branch"></i>';
        branchClass = 'branch-branches';
        branchBadgeClass = 'branches';
    } else if (branchInfo.startsWith('tags/')) {
        branchType = branchInfo.replace('tags/', '');
        // Truncate long tag names
        if (branchType.length > 15) {
            branchType = branchType.substring(0, 12) + '...';
        }
        branchIcon = '<i class="fas fa-tag"></i>';
        branchClass = 'branch-tags';
        branchBadgeClass = 'tags';
    } else {
        branchType = branchInfo || 'root';
        branchIcon = '<i class="fas fa-folder"></i>';
        branchClass = '';
        branchBadgeClass = '';
    }

    return {branchType, branchIcon, branchClass, branchBadgeClass};
}

function toggleRepoGroup(repoName) {
    const branches = document.getElementById(`branches-${repoName}`);
    const chevron = document.getElementById(`chevron-${repoName}`);

    if (branches.style.display === 'none') {
        branches.style.display = 'block';
        chevron.className = 'fas fa-chevron-down';
    } else {
        branches.style.display = 'none';
        chevron.className = 'fas fa-chevron-right';
    }
}

function filterResults() {
    const branchFilter = document.getElementById('branchFilter').value;
    const searchTerm = document.getElementById('repoSearch').value.toLowerCase();
    const rows = document.querySelectorAll('#repositoriesTable tr');

    let visibleCount = 0;

    rows.forEach(row => {
        const repoName = row.cells[0].textContent.toLowerCase();
        const branchType = row.cells[1].textContent.toLowerCase();

        // Check search term
        const matchesSearch = searchTerm === '' || repoName.includes(searchTerm);

        // Check branch filter
        let matchesFilter = true;
        if (branchFilter === 'trunk') {
            matchesFilter = branchType.includes('trunk');
        } else if (branchFilter === 'branches') {
            matchesFilter = branchType.includes('branches') || (branchType !== 'trunk' && !branchType.includes('tag'));
        } else if (branchFilter === 'tags') {
            matchesFilter = branchType.includes('tag');
        }

        if (matchesSearch && matchesFilter) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // Update results count
    const resultsCount = document.getElementById('resultsCount');
    resultsCount.textContent = `Showing ${visibleCount} of ${discoveredRepositories.length} repositories`;
}

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}

function showImportModal(index) {
    currentImportRepo = discoveredRepositories[index];
    
    document.getElementById('import_name').value = currentImportRepo.name;
    document.getElementById('import_url').value = currentImportRepo.url;
    document.getElementById('import_username').value = document.getElementById('username').value;
    document.getElementById('import_password').value = document.getElementById('password').value;
    
    new bootstrap.Modal(document.getElementById('importModal')).show();
}

function confirmImport() {
    if (!currentImportRepo) return;
    
    const importData = {
        ...currentImportRepo,
        username: document.getElementById('import_username').value || null,
        password: document.getElementById('import_password').value || null
    };
    
    fetch('/repositories/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(importData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Repository imported successfully!');
            bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();
            // Remove from discovered list
            const index = discoveredRepositories.indexOf(currentImportRepo);
            if (index > -1) {
                discoveredRepositories.splice(index, 1);
                displayResults(discoveredRepositories);
            }
        } else {
            alert('Import failed: ' + data.message);
        }
    })
    .catch(error => {
        alert('Network error: ' + error.message);
    });
}

function importAllRepositories() {
    if (discoveredRepositories.length === 0) return;
    
    if (!confirm(`Import all ${discoveredRepositories.length} repositories?`)) return;
    
    const username = document.getElementById('username').value || null;
    const password = document.getElementById('password').value || null;
    
    let imported = 0;
    let failed = 0;
    
    discoveredRepositories.forEach(repo => {
        const importData = {
            ...repo,
            username: username,
            password: password
        };
        
        fetch('/repositories/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(importData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                imported++;
            } else {
                failed++;
            }
            
            // Check if all requests completed
            if (imported + failed === discoveredRepositories.length) {
                alert(`Import completed: ${imported} successful, ${failed} failed`);
                if (imported > 0) {
                    discoveredRepositories = [];
                    displayResults([]);
                }
            }
        })
        .catch(error => {
            failed++;
            if (imported + failed === discoveredRepositories.length) {
                alert(`Import completed: ${imported} successful, ${failed} failed`);
            }
        });
    });
}

// Dynamic depth explanation
document.addEventListener('DOMContentLoaded', function() {
    const depthSelect = document.getElementById('max_depth');
    const depthExamples = {
        1: {
            title: "Shallow Search (Fast)",
            examples: [
                "/svn/<strong>repository1</strong>",
                "/svn/<strong>repository2</strong>",
                "/svn/<strong>project-name</strong>"
            ],
            description: "Only searches immediate subdirectories. Good for simple SVN setups where repositories are directly under the base URL."
        },
        2: {
            title: "Project Level Search",
            examples: [
                "/svn/project1/<strong>trunk</strong>",
                "/svn/project1/<strong>branches</strong>",
                "/svn/project2/<strong>trunk</strong>"
            ],
            description: "Searches 2 levels deep. Perfect for standard SVN project layouts with trunk/branches/tags structure."
        },
        3: {
            title: "Team/Department Structure (Recommended)",
            examples: [
                "/svn/team1/project1/<strong>trunk</strong>",
                "/svn/department/app/<strong>trunk</strong>",
                "/svn/group/service/<strong>branches</strong>"
            ],
            description: "Searches 3 levels deep. Ideal for organizations with team-based or department-based repository organization."
        },
        4: {
            title: "Deep Organizational Structure",
            examples: [
                "/svn/company/dept/team/<strong>project</strong>",
                "/svn/division/group/app/<strong>trunk</strong>",
                "/svn/org/unit/service/<strong>main</strong>"
            ],
            description: "Searches 4 levels deep. For complex organizational hierarchies with multiple nested levels."
        },
        5: {
            title: "Very Deep Hierarchy (Slow)",
            examples: [
                "/svn/corp/div/dept/team/<strong>project</strong>",
                "/svn/org/region/office/group/<strong>app</strong>",
                "/svn/company/business/unit/team/<strong>service</strong>"
            ],
            description: "Searches 5 levels deep. Use only for very complex structures. May be slow on large servers with many directories."
        }
    };

    function updateDepthExample() {
        const selectedDepth = depthSelect.value;
        const example = depthExamples[selectedDepth];

        if (example) {
            const helpText = document.querySelector('.depth-help');
            helpText.innerHTML = `
                <strong>${example.title}</strong><br>
                <small class="text-muted">
                    ${example.description}<br><br>
                    <strong>Example paths that would be found:</strong><br>
                    ${example.examples.map(ex => `&bull; ${ex}`).join('<br>')}
                </small>
            `;
        }
    }

    // Update on page load and when selection changes
    updateDepthExample();
    depthSelect.addEventListener('change', updateDepthExample);
});
</script>
{% endblock %}
