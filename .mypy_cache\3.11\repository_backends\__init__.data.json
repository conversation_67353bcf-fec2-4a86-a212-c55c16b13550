{".class": "MypyFile", "_fullname": "repository_backends", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RepositoryBackend": {".class": "SymbolTableNode", "cross_ref": "repository_backends.base.RepositoryBackend", "kind": "Gdef"}, "RepositoryBackendManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "repository_backends.RepositoryBackendManager", "name": "RepositoryBackendManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "repository_backends", "mro": ["repository_backends.RepositoryBackendManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager.__init__", "name": "__init__", "type": null}}, "_detect_repository_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager._detect_repository_type", "name": "_detect_repository_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "url"], "arg_types": ["repository_backends.RepositoryBackendManager", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_repository_type of RepositoryBackendManager", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_backends": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager._load_backends", "name": "_load_backends", "type": null}}, "backends": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "repository_backends.RepositoryBackendManager.backends", "name": "backends", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "repository_backends.base.RepositoryBackend"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "discover_repositories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "backend_type", "base_url", "config", "username", "password", "max_depth"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager.discover_repositories", "name": "discover_repositories", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "backend_type", "base_url", "config", "username", "password", "max_depth"], "arg_types": ["repository_backends.RepositoryBackendManager", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "discover_repositories of RepositoryBackendManager", "ret_type": {".class": "Instance", "args": ["repository_backends.base.RepositoryInfo"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_available_backends": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager.get_available_backends", "name": "get_available_backends", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["repository_backends.RepositoryBackendManager"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_available_backends of RepositoryBackendManager", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "backend_type", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager.get_backend", "name": "get_backend", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "backend_type", "config"], "arg_types": ["repository_backends.RepositoryBackendManager", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backend of RepositoryBackendManager", "ret_type": {".class": "UnionType", "items": ["repository_backends.base.RepositoryBackend", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_backend_for_repository": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager.get_backend_for_repository", "name": "get_backend_for_repository", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "repo", "config"], "arg_types": ["repository_backends.RepositoryBackendManager", "models.RepositoryConfig", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backend_for_repository of RepositoryBackendManager", "ret_type": {".class": "UnionType", "items": ["repository_backends.base.RepositoryBackend", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "repository_backends.RepositoryBackendManager.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "register_backend": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "backend_class"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.RepositoryBackendManager.register_backend", "name": "register_backend", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "backend_class"], "arg_types": ["repository_backends.RepositoryBackendManager", {".class": "TypeType", "item": "repository_backends.base.RepositoryBackend"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_backend of RepositoryBackendManager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "repository_backends.RepositoryBackendManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "repository_backends.RepositoryBackendManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RepositoryConfig": {".class": "SymbolTableNode", "cross_ref": "models.RepositoryConfig", "kind": "Gdef"}, "RepositoryInfo": {".class": "SymbolTableNode", "cross_ref": "repository_backends.base.RepositoryInfo", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "repository_backends.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "backend_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "repository_backends.backend_manager", "name": "backend_manager", "type": "repository_backends.RepositoryBackendManager"}}, "get_backend_manager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "repository_backends.get_backend_manager", "name": "get_backend_manager", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_backend_manager", "ret_type": "repository_backends.RepositoryBackendManager", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\repository_backends\\__init__.py"}