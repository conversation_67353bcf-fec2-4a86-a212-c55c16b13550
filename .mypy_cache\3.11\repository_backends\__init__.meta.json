{"data_mtime": 1754415321, "dep_lines": [9, 25, 33, 7, 8, 10, 1, 1, 1], "dep_prios": [5, 20, 20, 10, 5, 5, 5, 30, 30], "dependencies": ["repository_backends.base", "repository_backends.svn_backend", "repository_backends.git_backend", "logging", "typing", "models", "builtins", "_frozen_importlib", "abc"], "hash": "fe2fe1bfb51768c45923be5b0046a6076cfdb8af", "id": "repository_backends", "ignore_all": true, "interface_hash": "e82d1b434f4804692fc266b0ab5cb7aa1a897b7f", "mtime": 1754146065, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\repository_backends\\__init__.py", "plugin_data": null, "size": 5610, "suppressed": [], "version_id": "1.15.0"}