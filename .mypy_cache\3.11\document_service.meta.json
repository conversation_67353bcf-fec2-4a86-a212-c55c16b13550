{"data_mtime": 1754360855, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 18, 19, 20, 21, 22, 279, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "re", "json", "time", "logging", "pathlib", "typing", "dataclasses", "datetime", "document_database", "document_processor", "cache_manager", "ollama_client", "models", "shutil", "builtins", "_frozen_importlib", "_io", "abc", "enum", "io", "json.decoder", "types", "typing_extensions"], "hash": "1bc011a3c119fa9c525623dbe20647edef7e02b5", "id": "document_service", "ignore_all": false, "interface_hash": "1e46c2d8ba5bb783ff9147940cb49def151ed2fe", "mtime": 1754360878, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\document_service.py", "plugin_data": null, "size": 29416, "suppressed": [], "version_id": "1.15.0"}